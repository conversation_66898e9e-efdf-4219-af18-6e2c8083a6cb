---
import BaseLayout from '../../layouts/BaseLayout.astro';
import { getBooking } from '../../lib/pocketbase.ts';

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/dashboard');
}

// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}

// Get booking details server-side for SEO and initial load
let booking = null;
let error = null;

try {
  const result = await getBooking(id);
  if (result.success) {
    booking = result.booking;
  } else {
    error = result.error;
  }
} catch (err) {
  error = 'Failed to load booking';
}

// If booking not found, redirect to dashboard
if (!booking) {
  return Astro.redirect('/dashboard');
}

const pageTitle = `Booking: ${booking.venue.title}`;
const pageDescription = `Booking details for ${booking.venue.title} from ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}`;
---

<BaseLayout 
  title={pageTitle}
  description={pageDescription}
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <nav class="flex items-center space-x-2 text-sm text-slate-500 mb-2">
              <a href="/dashboard" class="hover:text-slate-700 transition-colors">Dashboard</a>
              <span>/</span>
              <a href="/dashboard" class="hover:text-slate-700 transition-colors">Bookings</a>
              <span>/</span>
              <span class="text-slate-900 font-medium">Booking Details</span>
            </nav>
            <h1 class="text-3xl font-bold text-slate-900">
              Booking Details
            </h1>
          </div>
          <a 
            href="/dashboard"
            class="inline-flex items-center px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-colors"
          >
            ← Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div id="booking-details-container" class="w-full">
        <!-- Loading State -->
        <div id="loading-state" class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="animate-pulse space-y-4">
            <div class="h-6 bg-slate-200 rounded w-1/3"></div>
            <div class="h-4 bg-slate-200 rounded w-1/2"></div>
            <div class="h-32 bg-slate-200 rounded"></div>
          </div>
        </div>

        <!-- Error State -->
        <div id="error-state" class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hidden">
          <div class="text-center">
            <div class="w-12 h-12 text-red-500 mx-auto mb-4">
              <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">
              Failed to load booking
            </h3>
            <p class="text-slate-600 mb-4">
              There was an error loading the booking details. Please try again.
            </p>
            <button 
              id="retry-button"
              class="inline-flex items-center px-4 py-2 border border-slate-300 rounded-lg text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- React Component Mount Point -->
  <script>
    import BookingDetails from '../../components/dashboard/BookingDetails.tsx';
    import { createRoot } from 'react-dom/client';
    import React from 'react';

    // Get booking ID from URL
    const pathParts = window.location.pathname.split('/');
    const bookingId = pathParts[pathParts.length - 1];

    // Get current user ID from auth store
    let currentUserId = null;
    try {
      const authData = localStorage.getItem('pocketbase_auth');
      if (authData) {
        const parsed = JSON.parse(authData);
        currentUserId = parsed.model?.id;
      }
    } catch (error) {
      console.error('Failed to get user ID:', error);
    }

    // Redirect if no user ID
    if (!currentUserId) {
      window.location.href = '/auth/login';
    }

    // Mount React component
    const container = document.getElementById('booking-details-container');
    if (container && currentUserId) {
      const root = createRoot(container);
      
      const handleBookingUpdate = (booking) => {
        // Update page title with booking info
        document.title = `Booking: ${booking.venue.title} - Trodoo`;
      };

      root.render(
        React.createElement(BookingDetails, {
          bookingId: bookingId,
          currentUserId: currentUserId,
          onBookingUpdate: handleBookingUpdate,
          className: 'w-full'
        })
      );
    }

    // Handle retry button
    document.getElementById('retry-button')?.addEventListener('click', () => {
      window.location.reload();
    });

    // Handle payment success from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const paymentStatus = urlParams.get('payment');
    const reference = urlParams.get('reference');

    if (paymentStatus === 'success' && reference) {
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
      notification.textContent = 'Payment successful! Your booking has been confirmed.';
      document.body.appendChild(notification);

      // Remove notification after 5 seconds
      setTimeout(() => {
        notification.remove();
      }, 5000);

      // Clean up URL parameters
      const cleanUrl = window.location.pathname;
      window.history.replaceState({}, document.title, cleanUrl);
    }
  </script>

  <!-- Paystack Script -->
  <script src="https://js.paystack.co/v1/inline.js"></script>

  <style>
    /* Custom styles for booking details page */
    .shadow-card {
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    /* Animation for status badges */
    .status-badge {
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Loading animation */
    .animate-pulse {
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: .5;
      }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .max-w-7xl {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }
  </style>
</BaseLayout>
